"""
订单表模型

用于记录量化交易系统中的所有订单信息，包括挂单、成交、取消等各种状态
"""

from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, Float, DateTime, Enum, Text, DECIMAL
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import enum

Base = declarative_base()


# 订单类型枚举
class OrderType(str, enum.Enum):
    """订单类型枚举"""
    MARKET = "market"           # 市价单
    LIMIT = "limit"             # 限价单
    STOP = "stop"               # 止损单
    STOP_LIMIT = "stop_limit"   # 止损限价单
    IOC = "ioc"                 # 立即成交或取消
    FOK = "fok"                 # 全部成交或取消
    GTC = "gtc"                 # 撤销前有效
    GTD = "gtd"                 # 指定日期前有效


# 订单方向枚举
class OrderDirection(str, enum.Enum):
    """订单方向枚举"""
    BUY = "buy"     # 买入
    SELL = "sell"   # 卖出


# 开平仓类型枚举
class OpenCloseType(str, enum.Enum):
    """开平仓类型枚举"""
    OPEN = "open"                   # 开仓
    CLOSE = "close"                 # 平仓
    CLOSE_TODAY = "close_today"     # 平今
    CLOSE_YESTERDAY = "close_yesterday"  # 平昨


# 订单状态枚举
class OrderStatus(str, enum.Enum):
    """订单状态枚举"""
    PENDING = "pending"         # 挂单中
    PARTIAL_FILLED = "partial_filled"  # 部分成交
    FILLED = "filled"           # 完全成交
    CANCELLED = "cancelled"     # 已取消
    REJECTED = "rejected"       # 已拒绝
    EXPIRED = "expired"         # 已过期


class Order(Base):
    """
    订单表模型
    
    统一管理所有订单状态，包括挂单、部分成交、完全成交、取消等状态。
    一个订单可以对应多笔交易记录。
    """
    
    __tablename__ = "orders"
    
    # 主键，自增ID
    id = Column(
        Integer, 
        primary_key=True, 
        autoincrement=True,
        comment="主键，自增ID"
    )
    
    # 订单号，唯一标识
    order_no = Column(
        String(64), 
        unique=True, 
        nullable=False, 
        index=True,
        comment="订单号，唯一标识"
    )
    
    # 订单时间
    order_time = Column(
        DateTime, 
        default=func.now(), 
        nullable=False,
        comment="订单创建时间"
    )
    
    # 渠道编号
    channel_code = Column(
        String(32), 
        nullable=False, 
        index=True,
        comment="渠道编号"
    )
    
    # 合约代码
    symbol = Column(
        String(32), 
        nullable=False, 
        index=True,
        comment="合约代码"
    )
    
    # 订单类型
    order_type = Column(
        Enum(OrderType), 
        nullable=False,
        comment="订单类型"
    )
    
    # 时效性
    time_in_force = Column(
        String(20), 
        nullable=True,
        comment="时效性（如GTC/FOK/IOC）"
    )
    
    # 订单价格
    order_price = Column(
        DECIMAL(18, 8), 
        nullable=True,
        comment="订单价格（市价单可为空）"
    )
    
    # 订单方向
    side = Column(
        Enum(OrderDirection), 
        nullable=False,
        comment="订单方向"
    )
    
    # 订单数量
    quantity = Column(
        DECIMAL(18, 8), 
        nullable=False,
        comment="订单数量"
    )
    
    # 开平仓类型
    open_close_type = Column(
        Enum(OpenCloseType), 
        nullable=False,
        comment="开平仓类型"
    )
    
    # 止损价格
    stop_loss_price = Column(
        DECIMAL(18, 8), 
        nullable=True,
        comment="止损价格"
    )
    
    # 止盈价格
    take_profit_price = Column(
        DECIMAL(18, 8), 
        nullable=True,
        comment="止盈价格"
    )
    
    # 已成交数量
    filled_quantity = Column(
        DECIMAL(18, 8), 
        default=0,
        comment="已成交数量"
    )
    
    # 订单状态
    status = Column(
        Enum(OrderStatus), 
        default=OrderStatus.PENDING,
        nullable=False,
        index=True,
        comment="订单状态"
    )
    
    # 近端交割日
    value_date = Column(
        String(8), 
        nullable=True,
        comment="近端交割日 (yyyyMMdd)"
    )
    
    # 远端交割日
    maturity_date = Column(
        String(8), 
        nullable=True,
        comment="远端交割日 (yyyyMMdd)"
    )
    
    # 创建时间
    create_time = Column(
        DateTime, 
        default=func.now(),
        nullable=False,
        comment="记录创建时间"
    )
    
    # 更新时间
    update_time = Column(
        DateTime, 
        default=func.now(),
        onupdate=func.now(),
        nullable=False,
        comment="记录更新时间"
    )
    
    # 订单参数
    order_params = Column(
        Text, 
        nullable=True,
        comment="订单参数（JSON格式）"
    )
    
    # 备注信息
    remark = Column(
        Text, 
        nullable=True,
        comment="备注信息"
    )
    
    # 关联到交易的关系
    trades = relationship("Trade", back_populates="order")
    
    def __repr__(self):
        """字符串表示"""
        return (
            f"<Order(id={self.id}, "
            f"order_no='{self.order_no}', "
            f"symbol='{self.symbol}', "
            f"side='{self.side}', "
            f"status='{self.status}', "
            f"quantity={self.quantity}, "
            f"filled_quantity={self.filled_quantity})>"
        )
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'order_no': self.order_no,
            'order_time': self.order_time.isoformat() if self.order_time else None,
            'channel_code': self.channel_code,
            'symbol': self.symbol,
            'order_type': self.order_type.value if self.order_type else None,
            'time_in_force': self.time_in_force,
            'order_price': float(self.order_price) if self.order_price else None,
            'side': self.side.value if self.side else None,
            'quantity': float(self.quantity) if self.quantity else None,
            'open_close_type': self.open_close_type.value if self.open_close_type else None,
            'stop_loss_price': float(self.stop_loss_price) if self.stop_loss_price else None,
            'take_profit_price': float(self.take_profit_price) if self.take_profit_price else None,
            'filled_quantity': float(self.filled_quantity) if self.filled_quantity else None,
            'status': self.status.value if self.status else None,
            'value_date': self.value_date,
            'maturity_date': self.maturity_date,
            'create_time': self.create_time.isoformat() if self.create_time else None,
            'update_time': self.update_time.isoformat() if self.update_time else None,
            'order_params': self.order_params,
            'remark': self.remark
        }

    @classmethod
    def from_dict(cls, data):
        """从字典创建实例"""
        from datetime import datetime

        # 处理时间字段
        if isinstance(data.get('order_time'), str):
            data['order_time'] = datetime.fromisoformat(data['order_time'])
        if isinstance(data.get('create_time'), str):
            data['create_time'] = datetime.fromisoformat(data['create_time'])
        if isinstance(data.get('update_time'), str):
            data['update_time'] = datetime.fromisoformat(data['update_time'])

        # 处理枚举字段
        if isinstance(data.get('order_type'), str):
            data['order_type'] = OrderType(data['order_type'])
        if isinstance(data.get('side'), str):
            data['side'] = OrderDirection(data['side'])
        if isinstance(data.get('open_close_type'), str):
            data['open_close_type'] = OpenCloseType(data['open_close_type'])
        if isinstance(data.get('status'), str):
            data['status'] = OrderStatus(data['status'])

        return cls(**data)
